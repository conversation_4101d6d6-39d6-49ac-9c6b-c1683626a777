{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UX Audit Report Evaluation Script\n", "\n", "This notebook evaluates the quality and accuracy of model-generated UX audit reports by comparing them with ground truth audit reports using the Gemini model.\n", "\n", "## Features:\n", "- ✅ Upload or load ground_truth.json and model_response.json\n", "- 🤖 Gemini API integration\n", "- 🧠 Semantic and fuzzy matching for intelligent comparison\n", "- 📈 Coverage score and detailed issue-level comparisons\n", "- 📦 Export results to JSON, CSV, or Excel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import numpy as np\n", "from typing import Dict, List, Tuple, Optional\n", "import google.generativeai as genai\n", "import os\n", "from datetime import datetime\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "GEMINI_API_KEY = \"YOUR_GEMINI_API_KEY_HERE\"  # Replace with your actual API key\n", "\n", "# Configure Gemini\n", "if GEMINI_API_KEY != \"YOUR_GEMINI_API_KEY_HERE\":\n", "    genai.configure(api_key=GEMINI_API_KEY)\n", "    model = genai.GenerativeModel('gemini-pro')\n", "else:\n", "    print(\"⚠️ Please set your Gemini API key in the GEMINI_API_KEY variable above\")\n", "    model = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_json_file(file_path: str) -> Dict:\n", "    \"\"\"Load JSON file and return parsed data.\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except FileNotFoundError:\n", "        print(f\"❌ File not found: {file_path}\")\n", "        return None\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ Invalid JSON in {file_path}: {e}\")\n", "        return None\n", "\n", "def validate_audit_structure(data: Dict, file_type: str) -> bool:\n", "    \"\"\"Validate the structure of audit report data.\"\"\"\n", "    if not data:\n", "        return False\n", "    \n", "    required_keys = ['audit_report']\n", "    if not all(key in data for key in required_keys):\n", "        print(f\"❌ Missing required keys in {file_type}\")\n", "        return False\n", "    \n", "    audit_report = data['audit_report']\n", "    if 'observations' not in audit_report:\n", "        print(f\"❌ Missing 'observations' in {file_type}\")\n", "        return False\n", "    \n", "    # Validate observation structure\n", "    required_obs_keys = ['id', 'severity', 'location', 'heuristics_violated', 'observation']\n", "    for obs in audit_report['observations']:\n", "        if not all(key in obs for key in required_obs_keys):\n", "            print(f\"❌ Missing required keys in observation {obs.get('id', 'unknown')} in {file_type}\")\n", "            return False\n", "    \n", "    return True\n", "\n", "# Load sample data\n", "print(\"📁 Loading sample data...\")\n", "ground_truth = load_json_file('ground_truth.json')\n", "model_response = load_json_file('model_response.json')\n", "\n", "if ground_truth and validate_audit_structure(ground_truth, 'ground_truth.json'):\n", "    print(\"✅ Ground truth loaded successfully\")\n", "    print(f\"   - {len(ground_truth['audit_report']['observations'])} observations\")\n", "\n", "if model_response and validate_audit_structure(model_response, 'model_response.json'):\n", "    print(\"✅ Model response loaded successfully\")\n", "    print(f\"   - {len(model_response['audit_report']['observations'])} observations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Gemini-Based Evaluation Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def gemini_evaluate_observation_match(gt_obs: Dict, model_obs: Dict) -> Dict:\n", "    \"\"\"Use Gemini to comprehensively evaluate if two observations match and provide detailed metrics.\"\"\"\n", "    if not model:\n", "        return {\n", "            'is_match': <PERSON><PERSON><PERSON>,\n", "            'observation_accuracy': 0,\n", "            'location_accuracy': 0,\n", "            'heuristic_accuracy': 0,\n", "            'missing_heuristics': gt_obs['heuristics_violated'],\n", "            'reasoning': 'Gemini API not configured'\n", "        }\n", "    \n", "    prompt = f\"\"\"\n", "    You are a UX evaluation expert. Compare these two UX audit observations and determine if they refer to the same issue.\n", "    \n", "    GROUND TRUTH OBSERVATION:\n", "    - ID: {gt_obs['id']}\n", "    - Severity: {gt_obs['severity']}\n", "    - Location: {gt_obs['location']}\n", "    - Heuristics Violated: {gt_obs['heuristics_violated']}\n", "    - Observation: {gt_obs['observation']}\n", "    \n", "    MODEL OBSERVATION:\n", "    - ID: {model_obs['id']}\n", "    - Severity: {model_obs['severity']}\n", "    - Location: {model_obs['location']}\n", "    - Heuristics Violated: {model_obs['heuristics_violated']}\n", "    - Observation: {model_obs['observation']}\n", "    \n", "    Please evaluate and provide:\n", "    1. is_match: true/false - Do these observations refer to the same UX issue?\n", "    2. observation_accuracy: 0-100 - How well does the model observation capture the ground truth issue?\n", "    3. location_accuracy: 0-100 - How well do the locations match?\n", "    4. heuristic_accuracy: 0-100 - What percentage of ground truth heuristics are covered?\n", "    5. missing_heuristics: List of ground truth heuristics not covered by the model\n", "    6. reasoning: Detailed explanation of your evaluation\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"is_match\": true/false,\n", "        \"observation_accuracy\": <number 0-100>,\n", "        \"location_accuracy\": <number 0-100>,\n", "        \"heuristic_accuracy\": <number 0-100>,\n", "        \"missing_heuristics\": [\"list\", \"of\", \"missing\", \"heuristics\"],\n", "        \"reasoning\": \"detailed explanation\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        response_text = response.text.strip()\n", "        \n", "        # Clean JSON response\n", "        if response_text.startswith('```json'):\n", "            response_text = response_text[7:-3]\n", "        elif response_text.startswith('```'):\n", "            response_text = response_text[3:-3]\n", "        \n", "        result = json.loads(response_text)\n", "        return result\n", "        \n", "    except Exception as e:\n", "        print(f\"⚠️ Gemini API error for GT#{gt_obs['id']} vs Model#{model_obs['id']}: {e}\")\n", "        return {\n", "            'is_match': <PERSON><PERSON><PERSON>,\n", "            'observation_accuracy': 0,\n", "            'location_accuracy': 0,\n", "            'heuristic_accuracy': 0,\n", "            'missing_heuristics': gt_obs['heuristics_violated'],\n", "            'reasoning': f'API Error: {str(e)}'\n", "        }\n", "\n", "def gemini_find_best_matches(gt_observations: List[Dict], model_observations: List[Dict]) -> Dict:\n", "    \"\"\"Use Gemini to find the best matches between ALL ground truth and model observations.\"\"\"\n", "    print(\"🤖 Using Gemini AI to find cross-matches between all observations...\")\n", "    print(f\"   Evaluating {len(gt_observations)} GT observations against {len(model_observations)} Model observations\")\n", "    print(f\"   Total comparisons: {len(gt_observations) * len(model_observations)}\\n\")\n", "    \n", "    # Store all evaluations\n", "    all_evaluations = []\n", "    \n", "    # Compare every GT observation with every Model observation\n", "    for i, gt_obs in enumerate(gt_observations):\n", "        print(f\"📋 Evaluating GT#{gt_obs['id']} ({i+1}/{len(gt_observations)})...\")\n", "        \n", "        for j, model_obs in enumerate(model_observations):\n", "            print(f\"   🤖 vs Model#{model_obs['id']} ({j+1}/{len(model_observations)})...\")\n", "            \n", "            # Get Gemini evaluation\n", "            evaluation = gemini_evaluate_observation_match(gt_obs, model_obs)\n", "            \n", "            # Calculate combined score\n", "            combined_score = (\n", "                evaluation['observation_accuracy'] * 0.5 +\n", "                evaluation['location_accuracy'] * 0.3 +\n", "                evaluation['heuristic_accuracy'] * 0.2\n", "            ) / 100.0\n", "            \n", "            all_evaluations.append({\n", "                'gt_id': gt_obs['id'],\n", "                'model_id': model_obs['id'],\n", "                'is_match': evaluation['is_match'],\n", "                'combined_score': combined_score,\n", "                'evaluation': evaluation\n", "            })\n", "            \n", "            match_status = \"✅ MATCH\" if evaluation['is_match'] else \"❌ No match\"\n", "            print(f\"      {match_status} (score: {combined_score:.3f})\")\n", "            \n", "            # Add delay to avoid rate limiting\n", "            time.sleep(0.5)\n", "        \n", "        print()\n", "    \n", "    # Find best matches for each GT observation\n", "    gt_matches = {}\n", "    used_model_ids = set()\n", "    \n", "    # Sort all evaluations by score (highest first)\n", "    sorted_evaluations = sorted(all_evaluations, key=lambda x: x['combined_score'], reverse=True)\n", "    \n", "    print(\"🎯 Finding best unique matches...\")\n", "    \n", "    # Assign best matches (each model can only match once)\n", "    for eval_data in sorted_evaluations:\n", "        gt_id = eval_data['gt_id']\n", "        model_id = eval_data['model_id']\n", "        \n", "        # Skip if GT already has a match or Model already used\n", "        if gt_id in gt_matches or model_id in used_model_ids:\n", "            continue\n", "        \n", "        # Only assign if it's a valid match\n", "        if eval_data['is_match'] and eval_data['combined_score'] > 0.3:  # Minimum threshold\n", "            gt_matches[gt_id] = {\n", "                'model_id': model_id,\n", "                'evaluation': eval_data['evaluation'],\n", "                'score': eval_data['combined_score']\n", "            }\n", "            used_model_ids.add(model_id)\n", "            print(f\"   ✅ GT#{gt_id} matched with Model#{model_id} (score: {eval_data['combined_score']:.3f})\")\n", "    \n", "    # Add unmatched GT observations\n", "    for gt_obs in gt_observations:\n", "        if gt_obs['id'] not in gt_matches:\n", "            gt_matches[gt_obs['id']] = {\n", "                'model_id': None,\n", "                'evaluation': {\n", "                    'is_match': <PERSON><PERSON><PERSON>,\n", "                    'observation_accuracy': 0,\n", "                    'location_accuracy': 0,\n", "                    'heuristic_accuracy': 0,\n", "                    'missing_heuristics': gt_obs['heuristics_violated'],\n", "                    'reasoning': 'No suitable match found in model response.'\n", "                },\n", "                'score': 0\n", "            }\n", "            print(f\"   ❌ GT#{gt_obs['id']} - No match found\")\n", "    \n", "    return {\n", "        'gt_matches': gt_matches,\n", "        'all_evaluations': all_evaluations,\n", "        'used_model_ids': used_model_ids\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Helper Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_severity_alignment(gt_severity: str, model_severity: str) -> str:\n", "    \"\"\"Determine severity alignment category.\"\"\"\n", "    severity_map = {'low': 1, 'medium': 2, 'high': 3}\n", "    \n", "    gt_level = severity_map.get(gt_severity.lower(), 2)\n", "    model_level = severity_map.get(model_severity.lower(), 2)\n", "    \n", "    diff = abs(gt_level - model_level)\n", "    \n", "    if diff == 0:\n", "        return \"Exact\"\n", "    elif diff == 1:\n", "        return \"Off by 1\"\n", "    else:\n", "        return \"Mismatch\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Core Evaluation Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def evaluate_single_observation(gt_obs: Dict, model_obs: Optional[Dict], gemini_evaluation: Dict) -> Dict:\n", "    \"\"\"Evaluate a single ground truth observation using <PERSON>'s assessment.\"\"\"\n", "    if not model_obs:\n", "        return {\n", "            'ground_truth_id': gt_obs['id'],\n", "            'model_match_id': None,\n", "            'coverage': <PERSON><PERSON><PERSON>,\n", "            'observation_accuracy': 0,\n", "            'heuristic_accuracy': 0,\n", "            'location_accuracy': 0,\n", "            'severity_alignment': 'Mismatch',\n", "            'sensitivity_score': 'Low',\n", "            'comments': {\n", "                'summary': 'Model did not capture this issue.',\n", "                'missing_heuristics': gt_obs['heuristics_violated'],\n", "                'gemini_reasoning': gemini_evaluation['reasoning']\n", "            }\n", "        }\n", "    \n", "    # Use Gemini's evaluation results\n", "    observation_acc = gemini_evaluation['observation_accuracy']\n", "    heuristic_acc = gemini_evaluation['heuristic_accuracy']\n", "    location_acc = gemini_evaluation['location_accuracy']\n", "    missing_heuristics = gemini_evaluation['missing_heuristics']\n", "    \n", "    # Calculate severity alignment\n", "    severity_align = get_severity_alignment(gt_obs['severity'], model_obs['severity'])\n", "    \n", "    # Determine sensitivity score based on Gemini's assessments\n", "    if observation_acc >= 80 and heuristic_acc >= 70:\n", "        sensitivity = 'High'\n", "    elif observation_acc >= 60 and heuristic_acc >= 50:\n", "        sensitivity = 'Medium'\n", "    else:\n", "        sensitivity = 'Low'\n", "    \n", "    return {\n", "        'ground_truth_id': gt_obs['id'],\n", "        'model_match_id': model_obs['id'],\n", "        'coverage': True,\n", "        'observation_accuracy': round(observation_acc, 1),\n", "        'heuristic_accuracy': round(heuristic_acc, 1),\n", "        'location_accuracy': round(location_acc, 1),\n", "        'severity_alignment': severity_align,\n", "        'sensitivity_score': sensitivity,\n", "        'comments': {\n", "            'summary': f\"Model {'fully' if sensitivity == 'High' else 'partially' if sensitivity == 'Medium' else 'poorly'} captured this issue.\",\n", "            'missing_heuristics': missing_heuristics,\n", "            'gemini_reasoning': gemini_evaluation['reasoning']\n", "        }\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Main Evaluation Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def evaluate_audit_reports(ground_truth_data: Dict, model_response_data: Dict) -> Dict:\n", "    \"\"\"Main function to evaluate model response against ground truth using Gemini AI.\"\"\"\n", "    gt_observations = ground_truth_data['audit_report']['observations']\n", "    model_observations = model_response_data['audit_report']['observations']\n", "    \n", "    print(f\"🔍 Evaluating {len(gt_observations)} ground truth observations against {len(model_observations)} model observations...\")\n", "    print(\"🤖 Using Gemini AI for comprehensive cross-evaluation...\\n\")\n", "    \n", "    # Find matches using <PERSON> (cross-evaluation)\n", "    matching_results = gemini_find_best_matches(gt_observations, model_observations)\n", "    gt_matches = matching_results['gt_matches']\n", "    all_evaluations = matching_results['all_evaluations']\n", "    used_model_ids = matching_results['used_model_ids']\n", "    \n", "    # Calculate coverage\n", "    covered_count = sum(1 for match_data in gt_matches.values() if match_data['model_id'] is not None)\n", "    coverage_score = covered_count / len(gt_observations)\n", "    \n", "    print(f\"\\n📊 Coverage: {covered_count}/{len(gt_observations)} ({coverage_score:.1%})\")\n", "    \n", "    # Evaluate each observation using Gemini's assessments\n", "    evaluation_results = []\n", "    \n", "    for gt_obs in gt_observations:\n", "        gt_id = gt_obs['id']\n", "        match_data = gt_matches[gt_id]\n", "        \n", "        # Find model observation if matched\n", "        model_obs = None\n", "        if match_data['model_id']:\n", "            model_obs = next((obs for obs in model_observations if obs['id'] == match_data['model_id']), None)\n", "        \n", "        # Evaluate using Gemini's assessment\n", "        result = evaluate_single_observation(gt_obs, model_obs, match_data['evaluation'])\n", "        \n", "        # Add the cross-matching information\n", "        if match_data['model_id']:\n", "            result['matched_model_id'] = match_data['model_id']\n", "            result['match_score'] = round(match_data['score'], 3)\n", "            result['comments']['match_info'] = f\"Ground Truth #{gt_id} matched with Model #{match_data['model_id']}\"\n", "        else:\n", "            result['matched_model_id'] = None\n", "            result['match_score'] = 0\n", "            result['comments']['match_info'] = f\"Ground Truth #{gt_id} - No suitable match found\"\n", "        \n", "        evaluation_results.append(result)\n", "    \n", "    # Create reverse mapping (which GT each model matched with)\n", "    model_to_gt_mapping = {}\n", "    for gt_id, match_data in gt_matches.items():\n", "        if match_data['model_id']:\n", "            model_to_gt_mapping[match_data['model_id']] = gt_id\n", "    \n", "    # Find unmatched model observations\n", "    unmatched_models = []\n", "    for model_obs in model_observations:\n", "        if model_obs['id'] not in used_model_ids:\n", "            unmatched_models.append({\n", "                'model_id': model_obs['id'],\n", "                'location': model_obs['location'],\n", "                'severity': model_obs['severity'],\n", "                'observation': model_obs['observation'][:200] + '...' if len(model_obs['observation']) > 200 else model_obs['observation']\n", "            })\n", "    \n", "    return {\n", "        'coverage_score': round(coverage_score, 3),\n", "        'evaluation_results': evaluation_results,\n", "        'cross_matching_summary': {\n", "            'model_to_gt_mapping': model_to_gt_mapping,\n", "            'unmatched_model_observations': unmatched_models,\n", "            'total_comparisons_made': len(all_evaluations)\n", "        },\n", "        'summary': {\n", "            'total_ground_truth': len(gt_observations),\n", "            'total_model_response': len(model_observations),\n", "            'covered_issues': covered_count,\n", "            'missed_issues': len(gt_observations) - covered_count,\n", "            'unmatched_model_observations': len(unmatched_models),\n", "            'evaluation_timestamp': datetime.now().isoformat()\n", "        }\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Debug and Test Gemini Evaluation (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def debug_gemini_evaluation(ground_truth_data: Dict, model_response_data: Dict, max_examples: int = 2):\n", "    \"\"\"Debug function to test Gemini evaluation on a few observations.\"\"\"\n", "    if not model:\n", "        print(\"⚠️ Gemini API not configured - skipping debug\")\n", "        return\n", "    \n", "    gt_observations = ground_truth_data['audit_report']['observations']\n", "    model_observations = model_response_data['audit_report']['observations']\n", "    \n", "    print(\"🔍 Debug: Testing Gemini Evaluation on Sample Observations\\n\")\n", "    \n", "    for i, gt_obs in enumerate(gt_observations[:max_examples]):\n", "        print(f\"📋 Testing Ground Truth #{gt_obs['id']}:\")\n", "        print(f\"   Location: {gt_obs['location']}\")\n", "        print(f\"   Severity: {gt_obs['severity']}\")\n", "        print(f\"   Observation: {gt_obs['observation'][:150]}...\")\n", "        print()\n", "        \n", "        # Test against first few model observations\n", "        for j, model_obs in enumerate(model_observations[:3]):\n", "            print(f\"   🤖 Testing against Model #{model_obs['id']}:\")\n", "            print(f\"      Location: {model_obs['location']}\")\n", "            print(f\"      Severity: {model_obs['severity']}\")\n", "            print(f\"      Observation: {model_obs['observation'][:150]}...\")\n", "            \n", "            # Get Gemini evaluation\n", "            evaluation = gemini_evaluate_observation_match(gt_obs, model_obs)\n", "            \n", "            print(f\"      📊 Gemini Evaluation:\")\n", "            print(f\"         Is Match: {evaluation['is_match']}\")\n", "            print(f\"         Observation Accuracy: {evaluation['observation_accuracy']}%\")\n", "            print(f\"         Location Accuracy: {evaluation['location_accuracy']}%\")\n", "            print(f\"         Heuristic Accuracy: {evaluation['heuristic_accuracy']}%\")\n", "            print(f\"         Missing Heuristics: {len(evaluation['missing_heuristics'])} items\")\n", "            print(f\"         Reasoning: {evaluation['reasoning'][:200]}...\")\n", "            print()\n", "            \n", "            time.sleep(1)  # Rate limiting\n", "        \n", "        print(\"   \" + \"=\"*80)\n", "        print()\n", "\n", "# Uncomment the line below to run debug analysis (uses Gemini API calls)\n", "# if ground_truth and model_response:\n", "#     debug_gemini_evaluation(ground_truth, model_response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Run Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the evaluation\n", "if ground_truth and model_response:\n", "    print(\"🚀 Starting evaluation...\\n\")\n", "    \n", "    evaluation_result = evaluate_audit_reports(ground_truth, model_response)\n", "    \n", "    print(f\"\\n🎯 Evaluation Complete!\")\n", "    print(f\"📈 Coverage Score: {evaluation_result['coverage_score']:.1%}\")\n", "    print(f\"📊 Summary: {evaluation_result['summary']}\")\n", "else:\n", "    print(\"❌ Cannot run evaluation - missing or invalid data files\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_results(evaluation_result: Dict) -> pd.DataFrame:\n", "    \"\"\"Convert evaluation results to DataFrame for analysis.\"\"\"\n", "    results_data = []\n", "    \n", "    for result in evaluation_result['evaluation_results']:\n", "        results_data.append({\n", "            'Ground_Truth_ID': result['ground_truth_id'],\n", "            'Model_Match_ID': result.get('model_match_id', 'None'),\n", "            'Coverage': result.get('coverage', False),\n", "            'Observation_Accuracy': result.get('observation_accuracy', 0),\n", "            'Heuristic_Accuracy': result.get('heuristic_accuracy', 0),\n", "            'Location_Accuracy': result.get('location_accuracy', 0),\n", "            'Severity_Alignment': result.get('severity_alignment', 'Mismatch'),\n", "            'Sensitivity_Score': result.get('sensitivity_score', 'Low'),\n", "            'Summary': result['comments']['summary'],\n", "            'Missing_Heuristics_Count': len(result['comments']['missing_heuristics']),\n", "            'Gemini_Reasoning': result['comments']['gemini_reasoning']\n", "        })\n", "    \n", "    return pd.DataFrame(results_data)\n", "\n", "# Create analysis DataFrame\n", "if 'evaluation_result' in locals():\n", "    df_results = analyze_results(evaluation_result)\n", "    \n", "    print(\"📊 Results Summary:\")\n", "    print(f\"Coverage Rate: {df_results['Coverage'].mean():.1%}\")\n", "    print(f\"Average Observation Accuracy: {df_results['Observation_Accuracy'].mean():.1f}%\")\n", "    print(f\"Average Heuristic Accuracy: {df_results['Heuristic_Accuracy'].mean():.1f}%\")\n", "    print(f\"Average Location Accuracy: {df_results['Location_Accuracy'].mean():.1f}%\")\n", "    \n", "    print(\"\\n📈 Severity Alignment Distribution:\")\n", "    print(df_results['Severity_Alignment'].value_counts())\n", "    \n", "    print(\"\\n🎯 Sensitivity Score Distribution:\")\n", "    print(df_results['Sensitivity_Score'].value_counts())\n", "    \n", "    # Display detailed results\n", "    print(\"\\n📋 Detailed Results:\")\n", "    display(df_results)\n", "    \n", "    # Display cross-matching information\n", "    if 'cross_matching_summary' in evaluation_result:\n", "        print(\"\\n🔄 Cross-Matching Summary:\")\n", "        mapping = evaluation_result['cross_matching_summary']['model_to_gt_mapping']\n", "        if mapping:\n", "            print(\"   Model ID → Ground Truth ID mappings:\")\n", "            for model_id, gt_id in mapping.items():\n", "                print(f\"      Model #{model_id} ↔ Ground Truth #{gt_id}\")\n", "        else:\n", "            print(\"   No successful matches found\")\n", "        \n", "        unmatched = evaluation_result['cross_matching_summary']['unmatched_model_observations']\n", "        if unmatched:\n", "            print(f\"\\n   📝 Unmatched Model Observations ({len(unmatched)}):\")\n", "            for obs in unmatched:\n", "                print(f\"      Model #{obs['model_id']}: {obs['location']} - {obs['observation'][:100]}...\")\n", "        \n", "        total_comparisons = evaluation_result['cross_matching_summary']['total_comparisons_made']\n", "        print(f\"\\n   🔍 Total AI comparisons made: {total_comparisons}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Export Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_to_json(evaluation_result: Dict, filename: str = None) -> str:\n", "    \"\"\"Export evaluation results to JSON file.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.json\"\n", "    \n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(evaluation_result, f, indent=2, ensure_ascii=False)\n", "    \n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename\n", "\n", "def export_to_csv(df_results: pd.DataFrame, filename: str = None) -> str:\n", "    \"\"\"Export results DataFrame to CSV file.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.csv\"\n", "    \n", "    df_results.to_csv(filename, index=False, encoding='utf-8')\n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename\n", "\n", "def export_to_excel(evaluation_result: Dict, df_results: pd.DataFrame, filename: str = None) -> str:\n", "    \"\"\"Export results to Excel with multiple sheets and formatting.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.xlsx\"\n", "    \n", "    with pd.ExcelWriter(filename, engine='openpyxl') as writer:\n", "        # Summary sheet\n", "        summary_data = {\n", "            'Metric': ['Coverage Score', 'Total Ground Truth', 'Total Model Response', \n", "                      'Covered Issues', 'Missed Issues', 'Avg Observation Accuracy', \n", "                      'Avg Heuristic Accuracy', 'Avg Location Accuracy'],\n", "            'Value': [\n", "                f\"{evaluation_result['coverage_score']:.1%}\",\n", "                evaluation_result['summary']['total_ground_truth'],\n", "                evaluation_result['summary']['total_model_response'],\n", "                evaluation_result['summary']['covered_issues'],\n", "                evaluation_result['summary']['missed_issues'],\n", "                f\"{df_results['Observation_Accuracy'].mean():.1f}%\",\n", "                f\"{df_results['Heuristic_Accuracy'].mean():.1f}%\",\n", "                f\"{df_results['Location_Accuracy'].mean():.1f}%\"\n", "            ]\n", "        }\n", "        pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)\n", "        \n", "        # Detailed results sheet\n", "        df_results.to_excel(writer, sheet_name='Detailed_Results', index=False)\n", "        \n", "        # Raw evaluation data\n", "        raw_df = pd.json_normalize(evaluation_result['evaluation_results'])\n", "        raw_df.to_excel(writer, sheet_name='Raw_Data', index=False)\n", "    \n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results in multiple formats\n", "if 'evaluation_result' in locals() and 'df_results' in locals():\n", "    print(\"📦 Exporting results...\")\n", "    \n", "    # Export to JSON\n", "    json_file = export_to_json(evaluation_result)\n", "    \n", "    # Export to CSV\n", "    csv_file = export_to_csv(df_results)\n", "    \n", "    # Export to Excel\n", "    excel_file = export_to_excel(evaluation_result, df_results)\n", "    \n", "    print(f\"\\n📁 Files created:\")\n", "    print(f\"   📄 JSON: {json_file}\")\n", "    print(f\"   📊 CSV: {csv_file}\")\n", "    print(f\"   📈 Excel: {excel_file}\")\n", "else:\n", "    print(\"❌ No evaluation results to export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Interactive File Upload (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Interactive file upload for custom files\n", "def load_custom_files():\n", "    \"\"\"Interactive function to load custom ground truth and model response files.\"\"\"\n", "    print(\"📁 Custom File Loading\")\n", "    print(\"Enter file paths (press Enter to use default files):\")\n", "    \n", "    gt_path = input(\"Ground truth file path: \").strip()\n", "    if not gt_path:\n", "        gt_path = 'ground_truth.json'\n", "    \n", "    model_path = input(\"Model response file path: \").strip()\n", "    if not model_path:\n", "        model_path = 'model_response.json'\n", "    \n", "    # Load files\n", "    custom_gt = load_json_file(gt_path)\n", "    custom_model = load_json_file(model_path)\n", "    \n", "    if (custom_gt and validate_audit_structure(custom_gt, gt_path) and \n", "        custom_model and validate_audit_structure(custom_model, model_path)):\n", "        \n", "        print(\"\\n🚀 Running evaluation on custom files...\")\n", "        custom_result = evaluate_audit_reports(custom_gt, custom_model)\n", "        custom_df = analyze_results(custom_result)\n", "        \n", "        # Export custom results\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        export_to_excel(custom_result, custom_df, f\"custom_evaluation_{timestamp}.xlsx\")\n", "        \n", "        return custom_result, custom_df\n", "    else:\n", "        print(\"❌ Failed to load or validate custom files\")\n", "        return None, None\n", "\n", "# Uncomment the line below to run interactive file loading\n", "# custom_result, custom_df = load_custom_files()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Configuration and Customization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gemini API Configuration\n", "def update_gemini_config(api_key: str = None):\n", "    \"\"\"Update Gemini API configuration.\"\"\"\n", "    global GEMINI_API_KEY, model\n", "    \n", "    if api_key:\n", "        GEMINI_API_KEY = api_key\n", "        genai.configure(api_key=GEMINI_API_KEY)\n", "        model = genai.GenerativeModel('gemini-pro')\n", "        print(f\"✅ Gemini API key updated and model configured\")\n", "    else:\n", "        print(f\"⚠️ Please provide a valid API key\")\n", "\n", "# Example: Update API key\n", "# update_gemini_config(\"your-new-api-key-here\")\n", "\n", "print(\"\\n🎛️ Current Configuration:\")\n", "print(f\"   Gemini API Configured: {'Yes' if model else 'No'}\")\n", "print(f\"   Evaluation Method: 100% Gemini AI-based\")\n", "print(f\"   Rate Limiting: 0.5s delay between API calls\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 15. Usage Instructions and Examples\n", "\n", "### Quick Start:\n", "1. **Set up Gemini API**: Replace `YOUR_GEMINI_API_KEY_HERE` with your actual API key\n", "2. **Load data**: Place your `ground_truth.json` and `model_response.json` files in the same directory\n", "3. **Run evaluation**: Execute all cells in order\n", "4. **View results**: Check the analysis output and exported files\n", "\n", "### File Format Requirements:\n", "Both JSON files should follow this structure:\n", "```json\n", "{\n", "  \"audit_report\": {\n", "    \"title\": \"Audit Report\",\n", "    \"observations\": [\n", "      {\n", "        \"id\": 1,\n", "        \"severity\": \"High|Medium|Low\",\n", "        \"location\": \"Description of location\",\n", "        \"heuristics_violated\": [\"List of heuristics\"],\n", "        \"observation\": \"Detailed observation text\"\n", "      }\n", "    ]\n", "  }\n", "}\n", "```\n", "\n", "### Customization Options:\n", "- **Thresholds**: Adjust similarity thresholds using `update_evaluation_config()`\n", "- **Custom files**: Use `load_custom_files()` for different input files\n", "- **Export formats**: Results are automatically exported to JSON, CSV, and Excel\n", "\n", "### Output Files:\n", "- **JSON**: Complete evaluation results with all metadata\n", "- **CSV**: Simplified tabular format for analysis\n", "- **Excel**: Multi-sheet workbook with summary, detailed results, and raw data\n", "\n", "### Troubleshooting:\n", "- **Gemini API errors**: Check your API key and internet connection\n", "- **File format errors**: Validate JSON structure against the required format\n", "- **Low coverage scores**: Consider adjusting similarity thresholds\n", "\n", "---\n", "\n", "**🎯 Evaluation Complete!** \n", "\n", "This notebook provides comprehensive evaluation of UX audit reports with:\n", "- ✅ Semantic similarity analysis using Gemini AI\n", "- ✅ Fuzzy location matching\n", "- ✅ Heuristic overlap detection\n", "- ✅ Coverage and accuracy metrics\n", "- ✅ Multiple export formats\n", "- ✅ Detailed reasoning and suggestions\n", "\n", "For questions or improvements, refer to the function documentation above."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}