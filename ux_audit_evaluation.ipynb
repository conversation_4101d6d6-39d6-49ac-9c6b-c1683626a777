# Install required packages
!pip install google-generativeai pandas openpyxl fuzzywuzzy python-levenshtein scikit-learn numpy

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from fuzzywuzzy import fuzz
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import google.generativeai as genai
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configuration
GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"  # Replace with your actual API key
SEMANTIC_SIMILARITY_THRESHOLD = 0.2  # Lowered for better matching
LOCATION_SIMILARITY_THRESHOLD = 0.3  # Lowered for better matching
HEURISTIC_OVERLAP_THRESHOLD = 0  # Allow matches without heuristic overlap

# Configure Gemini
if GEMINI_API_KEY != "YOUR_GEMINI_API_KEY_HERE":
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-pro')
else:
    print("⚠️ Please set your Gemini API key in the GEMINI_API_KEY variable above")
    model = None

def load_json_file(file_path: str) -> Dict:
    """Load JSON file and return parsed data."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {file_path}: {e}")
        return None

def validate_audit_structure(data: Dict, file_type: str) -> bool:
    """Validate the structure of audit report data."""
    if not data:
        return False
    
    required_keys = ['audit_report']
    if not all(key in data for key in required_keys):
        print(f"❌ Missing required keys in {file_type}")
        return False
    
    audit_report = data['audit_report']
    if 'observations' not in audit_report:
        print(f"❌ Missing 'observations' in {file_type}")
        return False
    
    # Validate observation structure
    required_obs_keys = ['id', 'severity', 'location', 'heuristics_violated', 'observation']
    for obs in audit_report['observations']:
        if not all(key in obs for key in required_obs_keys):
            print(f"❌ Missing required keys in observation {obs.get('id', 'unknown')} in {file_type}")
            return False
    
    return True

# Load sample data
print("📁 Loading sample data...")
ground_truth = load_json_file('ground_truth.json')
model_response = load_json_file('model_response.json')

if ground_truth and validate_audit_structure(ground_truth, 'ground_truth.json'):
    print("✅ Ground truth loaded successfully")
    print(f"   - {len(ground_truth['audit_report']['observations'])} observations")

if model_response and validate_audit_structure(model_response, 'model_response.json'):
    print("✅ Model response loaded successfully")
    print(f"   - {len(model_response['audit_report']['observations'])} observations")

def calculate_semantic_similarity(text1: str, text2: str) -> float:
    """Calculate semantic similarity using TF-IDF and cosine similarity."""
    if not text1 or not text2:
        return 0.0
    
    vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)
    try:
        tfidf_matrix = vectorizer.fit_transform([text1, text2])
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        return similarity
    except:
        return 0.0

def calculate_location_similarity(loc1: str, loc2: str) -> float:
    """Calculate location similarity using fuzzy matching."""
    if not loc1 or not loc2:
        return 0.0
    
    # Normalize locations
    loc1_norm = loc1.lower().strip()
    loc2_norm = loc2.lower().strip()
    
    # Use fuzzy ratio
    return fuzz.ratio(loc1_norm, loc2_norm) / 100.0

def calculate_heuristic_accuracy(gt_heuristics: List[str], model_heuristics: List[str]) -> Tuple[float, List[str]]:
    """Calculate heuristic matching accuracy."""
    if not gt_heuristics:
        return 1.0 if not model_heuristics else 0.0, []
    
    if not model_heuristics:
        return 0.0, gt_heuristics
    
    # Normalize heuristics for comparison
    gt_norm = [h.lower().strip() for h in gt_heuristics]
    model_norm = [h.lower().strip() for h in model_heuristics]
    
    matched = 0
    missing = []
    
    for gt_h in gt_heuristics:
        gt_h_norm = gt_h.lower().strip()
        found = False
        
        for model_h_norm in model_norm:
            # Check for exact match or partial match
            if (gt_h_norm == model_h_norm or 
                gt_h_norm in model_h_norm or 
                model_h_norm in gt_h_norm or
                fuzz.ratio(gt_h_norm, model_h_norm) > 80):
                matched += 1
                found = True
                break
        
        if not found:
            missing.append(gt_h)
    
    accuracy = matched / len(gt_heuristics)
    return accuracy, missing

def get_gemini_semantic_analysis(gt_obs: str, model_obs: str) -> Dict:
    """Use Gemini to analyze semantic similarity and provide reasoning."""
    if not model:
        return {
            'similarity_score': calculate_semantic_similarity(gt_obs, model_obs) * 100,
            'reasoning': 'Gemini API not configured - using TF-IDF similarity'
        }
    
    prompt = f"""
    Compare these two UX audit observations and provide:
    1. A similarity score (0-100) based on semantic meaning
    2. Brief reasoning for the score
    
    Ground Truth Observation:
    {gt_obs}
    
    Model Observation:
    {model_obs}
    
    Respond in JSON format:
    {{
        "similarity_score": <number 0-100>,
        "reasoning": "<brief explanation>"
    }}
    """
    
    try:
        response = model.generate_content(prompt)
        # Extract JSON from response
        response_text = response.text.strip()
        if response_text.startswith('```json'):
            response_text = response_text[7:-3]
        elif response_text.startswith('```'):
            response_text = response_text[3:-3]
        
        return json.loads(response_text)
    except Exception as e:
        print(f"⚠️ Gemini API error: {e}")
        return {
            'similarity_score': calculate_semantic_similarity(gt_obs, model_obs) * 100,
            'reasoning': f'Gemini API error - using fallback similarity: {str(e)}'
        }

def get_severity_alignment(gt_severity: str, model_severity: str) -> str:
    """Determine severity alignment category."""
    severity_map = {'low': 1, 'medium': 2, 'high': 3}
    
    gt_level = severity_map.get(gt_severity.lower(), 2)
    model_level = severity_map.get(model_severity.lower(), 2)
    
    diff = abs(gt_level - model_level)
    
    if diff == 0:
        return "Exact"
    elif diff == 1:
        return "Off by 1"
    else:
        return "Mismatch"

def find_best_matches(gt_observations: List[Dict], model_observations: List[Dict]) -> List[Tuple[int, Optional[int], float]]:
    """Find best matches between ground truth and model observations."""
    matches = []
    used_model_ids = set()
    
    for gt_obs in gt_observations:
        best_match = None
        best_score = 0
        
        for model_obs in model_observations:
            if model_obs['id'] in used_model_ids:
                continue
            
            # Calculate combined similarity score
            semantic_sim = calculate_semantic_similarity(gt_obs['observation'], model_obs['observation'])
            location_sim = calculate_location_similarity(gt_obs['location'], model_obs['location'])
            heuristic_acc, _ = calculate_heuristic_accuracy(gt_obs['heuristics_violated'], model_obs['heuristics_violated'])
            
            # More flexible weighted combination - prioritize semantic similarity
            combined_score = (semantic_sim * 0.6 + location_sim * 0.3 + heuristic_acc * 0.1)
            
            # More flexible matching criteria
            if (combined_score > best_score and 
                (semantic_sim >= SEMANTIC_SIMILARITY_THRESHOLD or 
                 location_sim >= LOCATION_SIMILARITY_THRESHOLD or
                 combined_score >= 0.15)):
                best_score = combined_score
                best_match = model_obs['id']
        
        if best_match:
            used_model_ids.add(best_match)
        
        matches.append((gt_obs['id'], best_match, best_score))
    
    return matches

def evaluate_single_observation(gt_obs: Dict, model_obs: Optional[Dict]) -> Dict:
    """Evaluate a single ground truth observation against its model match."""
    if not model_obs:
        return {
            'ground_truth_id': gt_obs['id'],
            'model_match_id': None,
            'coverage': False,
            'observation_accuracy': 0,
            'heuristic_accuracy': 0,
            'location_accuracy': 0,
            'severity_alignment': 'Mismatch',
            'sensitivity_score': 'Low',
            'comments': {
                'summary': 'Model did not capture this issue.',
                'missing_heuristics': gt_obs['heuristics_violated'],
                'gemini_reasoning': 'No matching observation found in model response.'
            }
        }
    
    # Calculate metrics
    gemini_analysis = get_gemini_semantic_analysis(gt_obs['observation'], model_obs['observation'])
    heuristic_acc, missing_heuristics = calculate_heuristic_accuracy(
        gt_obs['heuristics_violated'], model_obs['heuristics_violated']
    )
    location_acc = calculate_location_similarity(gt_obs['location'], model_obs['location'])
    severity_align = get_severity_alignment(gt_obs['severity'], model_obs['severity'])
    
    # Determine sensitivity score
    if gemini_analysis['similarity_score'] >= 80 and heuristic_acc >= 0.7:
        sensitivity = 'High'
    elif gemini_analysis['similarity_score'] >= 60 and heuristic_acc >= 0.5:
        sensitivity = 'Medium'
    else:
        sensitivity = 'Low'
    
    return {
        'ground_truth_id': gt_obs['id'],
        'model_match_id': model_obs['id'],
        'coverage': True,
        'observation_accuracy': round(gemini_analysis['similarity_score'], 1),
        'heuristic_accuracy': round(heuristic_acc * 100, 1),
        'location_accuracy': round(location_acc * 100, 1),
        'severity_alignment': severity_align,
        'sensitivity_score': sensitivity,
        'comments': {
            'summary': f"Model {'fully' if sensitivity == 'High' else 'partially' if sensitivity == 'Medium' else 'poorly'} captured this issue.",
            'missing_heuristics': missing_heuristics,
            'gemini_reasoning': gemini_analysis['reasoning']
        }
    }

def evaluate_audit_reports(ground_truth_data: Dict, model_response_data: Dict) -> Dict:
    """Main function to evaluate model response against ground truth."""
    gt_observations = ground_truth_data['audit_report']['observations']
    model_observations = model_response_data['audit_report']['observations']
    
    print(f"🔍 Evaluating {len(gt_observations)} ground truth observations against {len(model_observations)} model observations...")
    
    # Find matches
    matches = find_best_matches(gt_observations, model_observations)
    
    # Calculate coverage
    covered_count = sum(1 for _, match_id, _ in matches if match_id is not None)
    coverage_score = covered_count / len(gt_observations)
    
    print(f"📊 Coverage: {covered_count}/{len(gt_observations)} ({coverage_score:.1%})")
    
    # Evaluate each observation
    evaluation_results = []
    
    for gt_obs in gt_observations:
        # Find corresponding match
        match_id = None
        for gt_id, m_id, _ in matches:
            if gt_id == gt_obs['id']:
                match_id = m_id
                break
        
        # Find model observation
        model_obs = None
        if match_id:
            model_obs = next((obs for obs in model_observations if obs['id'] == match_id), None)
        
        # Evaluate
        result = evaluate_single_observation(gt_obs, model_obs)
        evaluation_results.append(result)
        
        print(f"✓ Evaluated GT#{gt_obs['id']} -> Model#{match_id or 'None'}")
    
    return {
        'coverage_score': round(coverage_score, 3),
        'evaluation_results': evaluation_results,
        'summary': {
            'total_ground_truth': len(gt_observations),
            'total_model_response': len(model_observations),
            'covered_issues': covered_count,
            'missed_issues': len(gt_observations) - covered_count,
            'evaluation_timestamp': datetime.now().isoformat()
        }
    }

def debug_matching(ground_truth_data: Dict, model_response_data: Dict, max_examples: int = 3):
    """Debug function to show similarity scores for the first few observations."""
    gt_observations = ground_truth_data['audit_report']['observations']
    model_observations = model_response_data['audit_report']['observations']
    
    print("🔍 Debug: Similarity Analysis for First Few Observations\n")
    
    for i, gt_obs in enumerate(gt_observations[:max_examples]):
        print(f"📋 Ground Truth #{gt_obs['id']}:")
        print(f"   Location: {gt_obs['location']}")
        print(f"   Observation: {gt_obs['observation'][:100]}...")
        print(f"   Heuristics: {gt_obs['heuristics_violated'][:2]}...")
        print()
        
        best_scores = []
        for model_obs in model_observations:
            semantic_sim = calculate_semantic_similarity(gt_obs['observation'], model_obs['observation'])
            location_sim = calculate_location_similarity(gt_obs['location'], model_obs['location'])
            heuristic_acc, _ = calculate_heuristic_accuracy(gt_obs['heuristics_violated'], model_obs['heuristics_violated'])
            combined_score = (semantic_sim * 0.6 + location_sim * 0.3 + heuristic_acc * 0.1)
            
            best_scores.append({
                'model_id': model_obs['id'],
                'semantic': semantic_sim,
                'location': location_sim,
                'heuristic': heuristic_acc,
                'combined': combined_score,
                'model_location': model_obs['location']
            })
        
        # Sort by combined score and show top 3
        best_scores.sort(key=lambda x: x['combined'], reverse=True)
        
        print("   🎯 Top 3 Model Matches:")
        for j, score in enumerate(best_scores[:3]):
            print(f"      {j+1}. Model #{score['model_id']} (Location: {score['model_location']})")
            print(f"         Semantic: {score['semantic']:.3f}, Location: {score['location']:.3f}, Heuristic: {score['heuristic']:.3f}")
            print(f"         Combined: {score['combined']:.3f}")
        
        print(f"   ✅ Match Found: {best_scores[0]['combined'] >= 0.15}")
        print("   " + "="*50)
        print()

# Run debug analysis
if ground_truth and model_response:
    debug_matching(ground_truth, model_response)

# Run the evaluation
if ground_truth and model_response:
    print("🚀 Starting evaluation...\n")
    
    evaluation_result = evaluate_audit_reports(ground_truth, model_response)
    
    print(f"\n🎯 Evaluation Complete!")
    print(f"📈 Coverage Score: {evaluation_result['coverage_score']:.1%}")
    print(f"📊 Summary: {evaluation_result['summary']}")
else:
    print("❌ Cannot run evaluation - missing or invalid data files")

def analyze_results(evaluation_result: Dict) -> pd.DataFrame:
    """Convert evaluation results to DataFrame for analysis."""
    results_data = []
    
    for result in evaluation_result['evaluation_results']:
        results_data.append({
            'Ground_Truth_ID': result['ground_truth_id'],
            'Model_Match_ID': result.get('model_match_id', 'None'),
            'Coverage': result.get('coverage', False),
            'Observation_Accuracy': result.get('observation_accuracy', 0),
            'Heuristic_Accuracy': result.get('heuristic_accuracy', 0),
            'Location_Accuracy': result.get('location_accuracy', 0),
            'Severity_Alignment': result.get('severity_alignment', 'Mismatch'),
            'Sensitivity_Score': result.get('sensitivity_score', 'Low'),
            'Summary': result['comments']['summary'],
            'Missing_Heuristics_Count': len(result['comments']['missing_heuristics']),
            'Gemini_Reasoning': result['comments']['gemini_reasoning']
        })
    
    return pd.DataFrame(results_data)

# Create analysis DataFrame
if 'evaluation_result' in locals():
    df_results = analyze_results(evaluation_result)
    
    print("📊 Results Summary:")
    print(f"Coverage Rate: {df_results['Coverage'].mean():.1%}")
    print(f"Average Observation Accuracy: {df_results['Observation_Accuracy'].mean():.1f}%")
    print(f"Average Heuristic Accuracy: {df_results['Heuristic_Accuracy'].mean():.1f}%")
    print(f"Average Location Accuracy: {df_results['Location_Accuracy'].mean():.1f}%")
    
    print("\n📈 Severity Alignment Distribution:")
    print(df_results['Severity_Alignment'].value_counts())
    
    print("\n🎯 Sensitivity Score Distribution:")
    print(df_results['Sensitivity_Score'].value_counts())
    
    # Display detailed results
    print("\n📋 Detailed Results:")
    display(df_results)

def export_to_json(evaluation_result: Dict, filename: str = None) -> str:
    """Export evaluation results to JSON file."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ux_audit_evaluation_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(evaluation_result, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Results exported to {filename}")
    return filename

def export_to_csv(df_results: pd.DataFrame, filename: str = None) -> str:
    """Export results DataFrame to CSV file."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ux_audit_evaluation_{timestamp}.csv"
    
    df_results.to_csv(filename, index=False, encoding='utf-8')
    print(f"✅ Results exported to {filename}")
    return filename

def export_to_excel(evaluation_result: Dict, df_results: pd.DataFrame, filename: str = None) -> str:
    """Export results to Excel with multiple sheets and formatting."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ux_audit_evaluation_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # Summary sheet
        summary_data = {
            'Metric': ['Coverage Score', 'Total Ground Truth', 'Total Model Response', 
                      'Covered Issues', 'Missed Issues', 'Avg Observation Accuracy', 
                      'Avg Heuristic Accuracy', 'Avg Location Accuracy'],
            'Value': [
                f"{evaluation_result['coverage_score']:.1%}",
                evaluation_result['summary']['total_ground_truth'],
                evaluation_result['summary']['total_model_response'],
                evaluation_result['summary']['covered_issues'],
                evaluation_result['summary']['missed_issues'],
                f"{df_results['Observation_Accuracy'].mean():.1f}%",
                f"{df_results['Heuristic_Accuracy'].mean():.1f}%",
                f"{df_results['Location_Accuracy'].mean():.1f}%"
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)
        
        # Detailed results sheet
        df_results.to_excel(writer, sheet_name='Detailed_Results', index=False)
        
        # Raw evaluation data
        raw_df = pd.json_normalize(evaluation_result['evaluation_results'])
        raw_df.to_excel(writer, sheet_name='Raw_Data', index=False)
    
    print(f"✅ Results exported to {filename}")
    return filename

# Export results in multiple formats
if 'evaluation_result' in locals() and 'df_results' in locals():
    print("📦 Exporting results...")
    
    # Export to JSON
    json_file = export_to_json(evaluation_result)
    
    # Export to CSV
    csv_file = export_to_csv(df_results)
    
    # Export to Excel
    excel_file = export_to_excel(evaluation_result, df_results)
    
    print(f"\n📁 Files created:")
    print(f"   📄 JSON: {json_file}")
    print(f"   📊 CSV: {csv_file}")
    print(f"   📈 Excel: {excel_file}")
else:
    print("❌ No evaluation results to export")

# Optional: Interactive file upload for custom files
def load_custom_files():
    """Interactive function to load custom ground truth and model response files."""
    print("📁 Custom File Loading")
    print("Enter file paths (press Enter to use default files):")
    
    gt_path = input("Ground truth file path: ").strip()
    if not gt_path:
        gt_path = 'ground_truth.json'
    
    model_path = input("Model response file path: ").strip()
    if not model_path:
        model_path = 'model_response.json'
    
    # Load files
    custom_gt = load_json_file(gt_path)
    custom_model = load_json_file(model_path)
    
    if (custom_gt and validate_audit_structure(custom_gt, gt_path) and 
        custom_model and validate_audit_structure(custom_model, model_path)):
        
        print("\n🚀 Running evaluation on custom files...")
        custom_result = evaluate_audit_reports(custom_gt, custom_model)
        custom_df = analyze_results(custom_result)
        
        # Export custom results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_to_excel(custom_result, custom_df, f"custom_evaluation_{timestamp}.xlsx")
        
        return custom_result, custom_df
    else:
        print("❌ Failed to load or validate custom files")
        return None, None

# Uncomment the line below to run interactive file loading
# custom_result, custom_df = load_custom_files()

# Advanced configuration options
def update_evaluation_config(**kwargs):
    """Update evaluation configuration parameters."""
    global SEMANTIC_SIMILARITY_THRESHOLD, LOCATION_SIMILARITY_THRESHOLD, HEURISTIC_OVERLAP_THRESHOLD
    
    if 'semantic_threshold' in kwargs:
        SEMANTIC_SIMILARITY_THRESHOLD = kwargs['semantic_threshold']
        print(f"✅ Semantic similarity threshold updated to {SEMANTIC_SIMILARITY_THRESHOLD}")
    
    if 'location_threshold' in kwargs:
        LOCATION_SIMILARITY_THRESHOLD = kwargs['location_threshold']
        print(f"✅ Location similarity threshold updated to {LOCATION_SIMILARITY_THRESHOLD}")
    
    if 'heuristic_threshold' in kwargs:
        HEURISTIC_OVERLAP_THRESHOLD = kwargs['heuristic_threshold']
        print(f"✅ Heuristic overlap threshold updated to {HEURISTIC_OVERLAP_THRESHOLD}")

# Example: Adjust thresholds for more/less strict matching
# update_evaluation_config(semantic_threshold=0.5, location_threshold=0.6)

print("\n🎛️ Current Configuration:")
print(f"   Semantic Similarity Threshold: {SEMANTIC_SIMILARITY_THRESHOLD}")
print(f"   Location Similarity Threshold: {LOCATION_SIMILARITY_THRESHOLD}")
print(f"   Heuristic Overlap Threshold: {HEURISTIC_OVERLAP_THRESHOLD}")