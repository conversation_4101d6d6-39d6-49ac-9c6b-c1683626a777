{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UX Audit Report Evaluation Script\n", "\n", "This notebook evaluates the quality and accuracy of model-generated UX audit reports by comparing them with ground truth audit reports using the Gemini model.\n", "\n", "## Features:\n", "- ✅ Upload or load ground_truth.json and model_response.json\n", "- 🤖 Gemini API integration\n", "- 🧠 Semantic and fuzzy matching for intelligent comparison\n", "- 📈 Coverage score and detailed issue-level comparisons\n", "- 📦 Export results to JSON, CSV, or Excel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas openpyxl fuzzywuzzy python-levenshtein scikit-learn numpy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import numpy as np\n", "from typing import Dict, List, Tuple, Optional\n", "from fuzzywuzzy import fuzz\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import google.generativeai as genai\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "GEMINI_API_KEY = \"YOUR_GEMINI_API_KEY_HERE\"  # Replace with your actual API key\n", "SEMANTIC_SIMILARITY_THRESHOLD = 0.6\n", "LOCATION_SIMILARITY_THRESHOLD = 0.7\n", "HEURISTIC_OVERLAP_THRESHOLD = 1  # Minimum number of overlapping heuristics\n", "\n", "# Configure Gemini\n", "if GEMINI_API_KEY != \"YOUR_GEMINI_API_KEY_HERE\":\n", "    genai.configure(api_key=GEMINI_API_KEY)\n", "    model = genai.GenerativeModel('gemini-pro')\n", "else:\n", "    print(\"⚠️ Please set your Gemini API key in the GEMINI_API_KEY variable above\")\n", "    model = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_json_file(file_path: str) -> Dict:\n", "    \"\"\"Load JSON file and return parsed data.\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except FileNotFoundError:\n", "        print(f\"❌ File not found: {file_path}\")\n", "        return None\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ Invalid JSON in {file_path}: {e}\")\n", "        return None\n", "\n", "def validate_audit_structure(data: Dict, file_type: str) -> bool:\n", "    \"\"\"Validate the structure of audit report data.\"\"\"\n", "    if not data:\n", "        return False\n", "    \n", "    required_keys = ['audit_report']\n", "    if not all(key in data for key in required_keys):\n", "        print(f\"❌ Missing required keys in {file_type}\")\n", "        return False\n", "    \n", "    audit_report = data['audit_report']\n", "    if 'observations' not in audit_report:\n", "        print(f\"❌ Missing 'observations' in {file_type}\")\n", "        return False\n", "    \n", "    # Validate observation structure\n", "    required_obs_keys = ['id', 'severity', 'location', 'heuristics_violated', 'observation']\n", "    for obs in audit_report['observations']:\n", "        if not all(key in obs for key in required_obs_keys):\n", "            print(f\"❌ Missing required keys in observation {obs.get('id', 'unknown')} in {file_type}\")\n", "            return False\n", "    \n", "    return True\n", "\n", "# Load sample data\n", "print(\"📁 Loading sample data...\")\n", "ground_truth = load_json_file('ground_truth.json')\n", "model_response = load_json_file('model_response.json')\n", "\n", "if ground_truth and validate_audit_structure(ground_truth, 'ground_truth.json'):\n", "    print(\"✅ Ground truth loaded successfully\")\n", "    print(f\"   - {len(ground_truth['audit_report']['observations'])} observations\")\n", "\n", "if model_response and validate_audit_structure(model_response, 'model_response.json'):\n", "    print(\"✅ Model response loaded successfully\")\n", "    print(f\"   - {len(model_response['audit_report']['observations'])} observations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Similarity and Matching Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_semantic_similarity(text1: str, text2: str) -> float:\n", "    \"\"\"Calculate semantic similarity using TF-IDF and cosine similarity.\"\"\"\n", "    if not text1 or not text2:\n", "        return 0.0\n", "    \n", "    vectorizer = TfidfVectorizer(stop_words='english', lowercase=True)\n", "    try:\n", "        tfidf_matrix = vectorizer.fit_transform([text1, text2])\n", "        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]\n", "        return similarity\n", "    except:\n", "        return 0.0\n", "\n", "def calculate_location_similarity(loc1: str, loc2: str) -> float:\n", "    \"\"\"Calculate location similarity using fuzzy matching.\"\"\"\n", "    if not loc1 or not loc2:\n", "        return 0.0\n", "    \n", "    # Normalize locations\n", "    loc1_norm = loc1.lower().strip()\n", "    loc2_norm = loc2.lower().strip()\n", "    \n", "    # Use fuzzy ratio\n", "    return fuzz.ratio(loc1_norm, loc2_norm) / 100.0\n", "\n", "def calculate_heuristic_accuracy(gt_heuristics: List[str], model_heuristics: List[str]) -> Tuple[float, List[str]]:\n", "    \"\"\"Calculate heuristic matching accuracy.\"\"\"\n", "    if not gt_heuristics:\n", "        return 1.0 if not model_heuristics else 0.0, []\n", "    \n", "    if not model_heuristics:\n", "        return 0.0, gt_heuristics\n", "    \n", "    # Normalize heuristics for comparison\n", "    gt_norm = [h.lower().strip() for h in gt_heuristics]\n", "    model_norm = [h.lower().strip() for h in model_heuristics]\n", "    \n", "    matched = 0\n", "    missing = []\n", "    \n", "    for gt_h in gt_heuristics:\n", "        gt_h_norm = gt_h.lower().strip()\n", "        found = False\n", "        \n", "        for model_h_norm in model_norm:\n", "            # Check for exact match or partial match\n", "            if (gt_h_norm == model_h_norm or \n", "                gt_h_norm in model_h_norm or \n", "                model_h_norm in gt_h_norm or\n", "                fuzz.ratio(gt_h_norm, model_h_norm) > 80):\n", "                matched += 1\n", "                found = True\n", "                break\n", "        \n", "        if not found:\n", "            missing.append(gt_h)\n", "    \n", "    accuracy = matched / len(gt_heuristics)\n", "    return accuracy, missing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Gemini Integration for Advanced Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_gemini_semantic_analysis(gt_obs: str, model_obs: str) -> Dict:\n", "    \"\"\"Use Gemini to analyze semantic similarity and provide reasoning.\"\"\"\n", "    if not model:\n", "        return {\n", "            'similarity_score': calculate_semantic_similarity(gt_obs, model_obs) * 100,\n", "            'reasoning': 'Gemini API not configured - using TF-IDF similarity'\n", "        }\n", "    \n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and provide:\n", "    1. A similarity score (0-100) based on semantic meaning\n", "    2. <PERSON><PERSON> reasoning for the score\n", "    \n", "    Ground Truth Observation:\n", "    {gt_obs}\n", "    \n", "    Model Observation:\n", "    {model_obs}\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"similarity_score\": <number 0-100>,\n", "        \"reasoning\": \"<brief explanation>\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        # Extract JSON from response\n", "        response_text = response.text.strip()\n", "        if response_text.startswith('```json'):\n", "            response_text = response_text[7:-3]\n", "        elif response_text.startswith('```'):\n", "            response_text = response_text[3:-3]\n", "        \n", "        return json.loads(response_text)\n", "    except Exception as e:\n", "        print(f\"⚠️ Gemini API error: {e}\")\n", "        return {\n", "            'similarity_score': calculate_semantic_similarity(gt_obs, model_obs) * 100,\n", "            'reasoning': f'Gemini API error - using fallback similarity: {str(e)}'\n", "        }\n", "\n", "def get_severity_alignment(gt_severity: str, model_severity: str) -> str:\n", "    \"\"\"Determine severity alignment category.\"\"\"\n", "    severity_map = {'low': 1, 'medium': 2, 'high': 3}\n", "    \n", "    gt_level = severity_map.get(gt_severity.lower(), 2)\n", "    model_level = severity_map.get(model_severity.lower(), 2)\n", "    \n", "    diff = abs(gt_level - model_level)\n", "    \n", "    if diff == 0:\n", "        return \"Exact\"\n", "    elif diff == 1:\n", "        return \"Off by 1\"\n", "    else:\n", "        return \"Mismatch\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Core Evaluation Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_best_matches(gt_observations: List[Dict], model_observations: List[Dict]) -> List[Tuple[int, Optional[int], float]]:\n", "    \"\"\"Find best matches between ground truth and model observations.\"\"\"\n", "    matches = []\n", "    used_model_ids = set()\n", "    \n", "    for gt_obs in gt_observations:\n", "        best_match = None\n", "        best_score = 0\n", "        \n", "        for model_obs in model_observations:\n", "            if model_obs['id'] in used_model_ids:\n", "                continue\n", "            \n", "            # Calculate combined similarity score\n", "            semantic_sim = calculate_semantic_similarity(gt_obs['observation'], model_obs['observation'])\n", "            location_sim = calculate_location_similarity(gt_obs['location'], model_obs['location'])\n", "            heuristic_acc, _ = calculate_heuristic_accuracy(gt_obs['heuristics_violated'], model_obs['heuristics_violated'])\n", "            \n", "            # Weighted combination\n", "            combined_score = (semantic_sim * 0.5 + location_sim * 0.3 + heuristic_acc * 0.2)\n", "            \n", "            if combined_score > best_score and combined_score >= SEMANTIC_SIMILARITY_THRESHOLD:\n", "                best_score = combined_score\n", "                best_match = model_obs['id']\n", "        \n", "        if best_match:\n", "            used_model_ids.add(best_match)\n", "        \n", "        matches.append((gt_obs['id'], best_match, best_score))\n", "    \n", "    return matches\n", "\n", "def evaluate_single_observation(gt_obs: Dict, model_obs: Optional[Dict]) -> Dict:\n", "    \"\"\"Evaluate a single ground truth observation against its model match.\"\"\"\n", "    if not model_obs:\n", "        return {\n", "            'ground_truth_id': gt_obs['id'],\n", "            'model_match_id': None,\n", "            'coverage': <PERSON><PERSON><PERSON>,\n", "            'observation_accuracy': 0,\n", "            'heuristic_accuracy': 0,\n", "            'location_accuracy': 0,\n", "            'severity_alignment': 'Mismatch',\n", "            'sensitivity_score': 'Low',\n", "            'comments': {\n", "                'summary': 'Model did not capture this issue.',\n", "                'missing_heuristics': gt_obs['heuristics_violated'],\n", "                'gemini_reasoning': 'No matching observation found in model response.'\n", "            }\n", "        }\n", "    \n", "    # Calculate metrics\n", "    gemini_analysis = get_gemini_semantic_analysis(gt_obs['observation'], model_obs['observation'])\n", "    heuristic_acc, missing_heuristics = calculate_heuristic_accuracy(\n", "        gt_obs['heuristics_violated'], model_obs['heuristics_violated']\n", "    )\n", "    location_acc = calculate_location_similarity(gt_obs['location'], model_obs['location'])\n", "    severity_align = get_severity_alignment(gt_obs['severity'], model_obs['severity'])\n", "    \n", "    # Determine sensitivity score\n", "    if gemini_analysis['similarity_score'] >= 80 and heuristic_acc >= 0.7:\n", "        sensitivity = 'High'\n", "    elif gemini_analysis['similarity_score'] >= 60 and heuristic_acc >= 0.5:\n", "        sensitivity = 'Medium'\n", "    else:\n", "        sensitivity = 'Low'\n", "    \n", "    return {\n", "        'ground_truth_id': gt_obs['id'],\n", "        'model_match_id': model_obs['id'],\n", "        'coverage': True,\n", "        'observation_accuracy': round(gemini_analysis['similarity_score'], 1),\n", "        'heuristic_accuracy': round(heuristic_acc * 100, 1),\n", "        'location_accuracy': round(location_acc * 100, 1),\n", "        'severity_alignment': severity_align,\n", "        'sensitivity_score': sensitivity,\n", "        'comments': {\n", "            'summary': f\"Model {'fully' if sensitivity == 'High' else 'partially' if sensitivity == 'Medium' else 'poorly'} captured this issue.\",\n", "            'missing_heuristics': missing_heuristics,\n", "            'gemini_reasoning': gemini_analysis['reasoning']\n", "        }\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Main Evaluation Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def evaluate_audit_reports(ground_truth_data: Dict, model_response_data: Dict) -> Dict:\n", "    \"\"\"Main function to evaluate model response against ground truth.\"\"\"\n", "    gt_observations = ground_truth_data['audit_report']['observations']\n", "    model_observations = model_response_data['audit_report']['observations']\n", "    \n", "    print(f\"🔍 Evaluating {len(gt_observations)} ground truth observations against {len(model_observations)} model observations...\")\n", "    \n", "    # Find matches\n", "    matches = find_best_matches(gt_observations, model_observations)\n", "    \n", "    # Calculate coverage\n", "    covered_count = sum(1 for _, match_id, _ in matches if match_id is not None)\n", "    coverage_score = covered_count / len(gt_observations)\n", "    \n", "    print(f\"📊 Coverage: {covered_count}/{len(gt_observations)} ({coverage_score:.1%})\")\n", "    \n", "    # Evaluate each observation\n", "    evaluation_results = []\n", "    \n", "    for gt_obs in gt_observations:\n", "        # Find corresponding match\n", "        match_id = None\n", "        for gt_id, m_id, _ in matches:\n", "            if gt_id == gt_obs['id']:\n", "                match_id = m_id\n", "                break\n", "        \n", "        # Find model observation\n", "        model_obs = None\n", "        if match_id:\n", "            model_obs = next((obs for obs in model_observations if obs['id'] == match_id), None)\n", "        \n", "        # Evaluate\n", "        result = evaluate_single_observation(gt_obs, model_obs)\n", "        evaluation_results.append(result)\n", "        \n", "        print(f\"✓ Evaluated GT#{gt_obs['id']} -> Model#{match_id or 'None'}\")\n", "    \n", "    return {\n", "        'coverage_score': round(coverage_score, 3),\n", "        'evaluation_results': evaluation_results,\n", "        'summary': {\n", "            'total_ground_truth': len(gt_observations),\n", "            'total_model_response': len(model_observations),\n", "            'covered_issues': covered_count,\n", "            'missed_issues': len(gt_observations) - covered_count,\n", "            'evaluation_timestamp': datetime.now().isoformat()\n", "        }\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Run Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the evaluation\n", "if ground_truth and model_response:\n", "    print(\"🚀 Starting evaluation...\\n\")\n", "    \n", "    evaluation_result = evaluate_audit_reports(ground_truth, model_response)\n", "    \n", "    print(f\"\\n🎯 Evaluation Complete!\")\n", "    print(f\"📈 Coverage Score: {evaluation_result['coverage_score']:.1%}\")\n", "    print(f\"📊 Summary: {evaluation_result['summary']}\")\n", "else:\n", "    print(\"❌ Cannot run evaluation - missing or invalid data files\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_results(evaluation_result: Dict) -> pd.DataFrame:\n", "    \"\"\"Convert evaluation results to DataFrame for analysis.\"\"\"\n", "    results_data = []\n", "    \n", "    for result in evaluation_result['evaluation_results']:\n", "        results_data.append({\n", "            'Ground_Truth_ID': result['ground_truth_id'],\n", "            'Model_Match_ID': result.get('model_match_id', 'None'),\n", "            'Coverage': result.get('coverage', False),\n", "            'Observation_Accuracy': result.get('observation_accuracy', 0),\n", "            'Heuristic_Accuracy': result.get('heuristic_accuracy', 0),\n", "            'Location_Accuracy': result.get('location_accuracy', 0),\n", "            'Severity_Alignment': result.get('severity_alignment', 'Mismatch'),\n", "            'Sensitivity_Score': result.get('sensitivity_score', 'Low'),\n", "            'Summary': result['comments']['summary'],\n", "            'Missing_Heuristics_Count': len(result['comments']['missing_heuristics']),\n", "            'Gemini_Reasoning': result['comments']['gemini_reasoning']\n", "        })\n", "    \n", "    return pd.DataFrame(results_data)\n", "\n", "# Create analysis DataFrame\n", "if 'evaluation_result' in locals():\n", "    df_results = analyze_results(evaluation_result)\n", "    \n", "    print(\"📊 Results Summary:\")\n", "    print(f\"Coverage Rate: {df_results['Coverage'].mean():.1%}\")\n", "    print(f\"Average Observation Accuracy: {df_results['Observation_Accuracy'].mean():.1f}%\")\n", "    print(f\"Average Heuristic Accuracy: {df_results['Heuristic_Accuracy'].mean():.1f}%\")\n", "    print(f\"Average Location Accuracy: {df_results['Location_Accuracy'].mean():.1f}%\")\n", "    \n", "    print(\"\\n📈 Severity Alignment Distribution:\")\n", "    print(df_results['Severity_Alignment'].value_counts())\n", "    \n", "    print(\"\\n🎯 Sensitivity Score Distribution:\")\n", "    print(df_results['Sensitivity_Score'].value_counts())\n", "    \n", "    # Display detailed results\n", "    print(\"\\n📋 Detailed Results:\")\n", "    display(df_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Export Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_to_json(evaluation_result: Dict, filename: str = None) -> str:\n", "    \"\"\"Export evaluation results to JSON file.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.json\"\n", "    \n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(evaluation_result, f, indent=2, ensure_ascii=False)\n", "    \n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename\n", "\n", "def export_to_csv(df_results: pd.DataFrame, filename: str = None) -> str:\n", "    \"\"\"Export results DataFrame to CSV file.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.csv\"\n", "    \n", "    df_results.to_csv(filename, index=False, encoding='utf-8')\n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename\n", "\n", "def export_to_excel(evaluation_result: Dict, df_results: pd.DataFrame, filename: str = None) -> str:\n", "    \"\"\"Export results to Excel with multiple sheets and formatting.\"\"\"\n", "    if not filename:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filename = f\"ux_audit_evaluation_{timestamp}.xlsx\"\n", "    \n", "    with pd.ExcelWriter(filename, engine='openpyxl') as writer:\n", "        # Summary sheet\n", "        summary_data = {\n", "            'Metric': ['Coverage Score', 'Total Ground Truth', 'Total Model Response', \n", "                      'Covered Issues', 'Missed Issues', 'Avg Observation Accuracy', \n", "                      'Avg Heuristic Accuracy', 'Avg Location Accuracy'],\n", "            'Value': [\n", "                f\"{evaluation_result['coverage_score']:.1%}\",\n", "                evaluation_result['summary']['total_ground_truth'],\n", "                evaluation_result['summary']['total_model_response'],\n", "                evaluation_result['summary']['covered_issues'],\n", "                evaluation_result['summary']['missed_issues'],\n", "                f\"{df_results['Observation_Accuracy'].mean():.1f}%\",\n", "                f\"{df_results['Heuristic_Accuracy'].mean():.1f}%\",\n", "                f\"{df_results['Location_Accuracy'].mean():.1f}%\"\n", "            ]\n", "        }\n", "        pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)\n", "        \n", "        # Detailed results sheet\n", "        df_results.to_excel(writer, sheet_name='Detailed_Results', index=False)\n", "        \n", "        # Raw evaluation data\n", "        raw_df = pd.json_normalize(evaluation_result['evaluation_results'])\n", "        raw_df.to_excel(writer, sheet_name='Raw_Data', index=False)\n", "    \n", "    print(f\"✅ Results exported to {filename}\")\n", "    return filename"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results in multiple formats\n", "if 'evaluation_result' in locals() and 'df_results' in locals():\n", "    print(\"📦 Exporting results...\")\n", "    \n", "    # Export to JSON\n", "    json_file = export_to_json(evaluation_result)\n", "    \n", "    # Export to CSV\n", "    csv_file = export_to_csv(df_results)\n", "    \n", "    # Export to Excel\n", "    excel_file = export_to_excel(evaluation_result, df_results)\n", "    \n", "    print(f\"\\n📁 Files created:\")\n", "    print(f\"   📄 JSON: {json_file}\")\n", "    print(f\"   📊 CSV: {csv_file}\")\n", "    print(f\"   📈 Excel: {excel_file}\")\n", "else:\n", "    print(\"❌ No evaluation results to export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Interactive File Upload (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Interactive file upload for custom files\n", "def load_custom_files():\n", "    \"\"\"Interactive function to load custom ground truth and model response files.\"\"\"\n", "    print(\"📁 Custom File Loading\")\n", "    print(\"Enter file paths (press Enter to use default files):\")\n", "    \n", "    gt_path = input(\"Ground truth file path: \").strip()\n", "    if not gt_path:\n", "        gt_path = 'ground_truth.json'\n", "    \n", "    model_path = input(\"Model response file path: \").strip()\n", "    if not model_path:\n", "        model_path = 'model_response.json'\n", "    \n", "    # Load files\n", "    custom_gt = load_json_file(gt_path)\n", "    custom_model = load_json_file(model_path)\n", "    \n", "    if (custom_gt and validate_audit_structure(custom_gt, gt_path) and \n", "        custom_model and validate_audit_structure(custom_model, model_path)):\n", "        \n", "        print(\"\\n🚀 Running evaluation on custom files...\")\n", "        custom_result = evaluate_audit_reports(custom_gt, custom_model)\n", "        custom_df = analyze_results(custom_result)\n", "        \n", "        # Export custom results\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        export_to_excel(custom_result, custom_df, f\"custom_evaluation_{timestamp}.xlsx\")\n", "        \n", "        return custom_result, custom_df\n", "    else:\n", "        print(\"❌ Failed to load or validate custom files\")\n", "        return None, None\n", "\n", "# Uncomment the line below to run interactive file loading\n", "# custom_result, custom_df = load_custom_files()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Configuration and Customization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced configuration options\n", "def update_evaluation_config(**kwargs):\n", "    \"\"\"Update evaluation configuration parameters.\"\"\"\n", "    global SEMANTIC_SIMILARITY_THRESHOLD, LOCATION_SIMILARITY_THRESHOLD, HEURISTIC_OVERLAP_THRESHOLD\n", "    \n", "    if 'semantic_threshold' in kwargs:\n", "        SEMANTIC_SIMILARITY_THRESHOLD = kwargs['semantic_threshold']\n", "        print(f\"✅ Semantic similarity threshold updated to {SEMANTIC_SIMILARITY_THRESHOLD}\")\n", "    \n", "    if 'location_threshold' in kwargs:\n", "        LOCATION_SIMILARITY_THRESHOLD = kwargs['location_threshold']\n", "        print(f\"✅ Location similarity threshold updated to {LOCATION_SIMILARITY_THRESHOLD}\")\n", "    \n", "    if 'heuristic_threshold' in kwargs:\n", "        HEURISTIC_OVERLAP_THRESHOLD = kwargs['heuristic_threshold']\n", "        print(f\"✅ Heuristic overlap threshold updated to {HEURISTIC_OVERLAP_THRESHOLD}\")\n", "\n", "# Example: Adjust thresholds for more/less strict matching\n", "# update_evaluation_config(semantic_threshold=0.5, location_threshold=0.6)\n", "\n", "print(\"\\n🎛️ Current Configuration:\")\n", "print(f\"   Semantic Similarity Threshold: {SEMANTIC_SIMILARITY_THRESHOLD}\")\n", "print(f\"   Location Similarity Threshold: {LOCATION_SIMILARITY_THRESHOLD}\")\n", "print(f\"   Heuristic Overlap Threshold: {HEURISTIC_OVERLAP_THRESHOLD}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Usage Instructions and Examples\n", "\n", "### Quick Start:\n", "1. **Set up Gemini API**: Replace `YOUR_GEMINI_API_KEY_HERE` with your actual API key\n", "2. **Load data**: Place your `ground_truth.json` and `model_response.json` files in the same directory\n", "3. **Run evaluation**: Execute all cells in order\n", "4. **View results**: Check the analysis output and exported files\n", "\n", "### File Format Requirements:\n", "Both JSON files should follow this structure:\n", "```json\n", "{\n", "  \"audit_report\": {\n", "    \"title\": \"Audit Report\",\n", "    \"observations\": [\n", "      {\n", "        \"id\": 1,\n", "        \"severity\": \"High|Medium|Low\",\n", "        \"location\": \"Description of location\",\n", "        \"heuristics_violated\": [\"List of heuristics\"],\n", "        \"observation\": \"Detailed observation text\"\n", "      }\n", "    ]\n", "  }\n", "}\n", "```\n", "\n", "### Customization Options:\n", "- **Thresholds**: Adjust similarity thresholds using `update_evaluation_config()`\n", "- **Custom files**: Use `load_custom_files()` for different input files\n", "- **Export formats**: Results are automatically exported to JSON, CSV, and Excel\n", "\n", "### Output Files:\n", "- **JSON**: Complete evaluation results with all metadata\n", "- **CSV**: Simplified tabular format for analysis\n", "- **Excel**: Multi-sheet workbook with summary, detailed results, and raw data\n", "\n", "### Troubleshooting:\n", "- **Gemini API errors**: Check your API key and internet connection\n", "- **File format errors**: Validate JSON structure against the required format\n", "- **Low coverage scores**: Consider adjusting similarity thresholds\n", "\n", "---\n", "\n", "**🎯 Evaluation Complete!** \n", "\n", "This notebook provides comprehensive evaluation of UX audit reports with:\n", "- ✅ Semantic similarity analysis using Gemini AI\n", "- ✅ Fuzzy location matching\n", "- ✅ Heuristic overlap detection\n", "- ✅ Coverage and accuracy metrics\n", "- ✅ Multiple export formats\n", "- ✅ Detailed reasoning and suggestions\n", "\n", "For questions or improvements, refer to the function documentation above."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}